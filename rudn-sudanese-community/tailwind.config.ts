import type { Config } from "tailwindcss";

const config: Config = {
  content: [
    "./src/pages/**/*.{js,ts,jsx,tsx,mdx}",
    "./src/components/**/*.{js,ts,jsx,tsx,mdx}",
    "./src/app/**/*.{js,ts,jsx,tsx,mdx}",
  ],
  theme: {
    extend: {
      colors: {
        primary: {
          DEFAULT: "#00ccff",
          50: "#f0fcff",
          100: "#e0f9ff",
          200: "#baf2ff",
          300: "#7de8ff",
          400: "#38daff",
          500: "#00ccff",
          600: "#00a3d1",
          700: "#0082a8",
          800: "#006b8a",
          900: "#065972",
        },
        success: {
          DEFAULT: "#66cd00",
          50: "#f4ffe0",
          100: "#e6ffb3",
          200: "#d1ff80",
          300: "#b8ff4d",
          400: "#9fff1a",
          500: "#66cd00",
          600: "#5cb300",
          700: "#4d9900",
          800: "#3d7a00",
          900: "#2e5c00",
        },
        error: {
          DEFAULT: "#ff4444",
          50: "#fff1f1",
          100: "#ffe1e1",
          200: "#ffc7c7",
          300: "#ffa0a0",
          400: "#ff6b6b",
          500: "#ff4444",
          600: "#ed1515",
          700: "#c80d0d",
          800: "#a50f0f",
          900: "#881414",
        },
        warning: {
          DEFAULT: "#ffaa00",
          50: "#fffbeb",
          100: "#fef3c7",
          200: "#fde68a",
          300: "#fcd34d",
          400: "#fbbf24",
          500: "#ffaa00",
          600: "#d97706",
          700: "#b45309",
          800: "#92400e",
          900: "#78350f",
        },
        border: "#66cd00",
        background: "#ffffff",
        foreground: "#000000",
      },
      fontFamily: {
        sans: ["Inter", "system-ui", "sans-serif"],
        arabic: ["Noto Sans Arabic", "system-ui", "sans-serif"],
      },
      animation: {
        "fade-in": "fadeIn 0.5s ease-in-out",
        "slide-up": "slideUp 0.3s ease-out",
        "slide-down": "slideDown 0.3s ease-out",
      },
      keyframes: {
        fadeIn: {
          "0%": { opacity: "0" },
          "100%": { opacity: "1" },
        },
        slideUp: {
          "0%": { transform: "translateY(10px)", opacity: "0" },
          "100%": { transform: "translateY(0)", opacity: "1" },
        },
        slideDown: {
          "0%": { transform: "translateY(-10px)", opacity: "0" },
          "100%": { transform: "translateY(0)", opacity: "1" },
        },
      },
    },
  },
  plugins: [],
};
export default config;
