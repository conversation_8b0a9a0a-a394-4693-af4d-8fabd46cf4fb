{"version": 3, "sources": [], "sections": [{"offset": {"line": 4, "column": 0}, "map": {"version": 3, "sources": ["file:///run/media/ceazer/982f53f4-3e3b-4aa6-8864-4f0194b752d01/Rudn/rudn-sudanese-community/src/components/providers/session-provider.tsx"], "sourcesContent": ["'use client'\n\nimport { SessionProvider } from 'next-auth/react'\nimport { ReactNode } from 'react'\n\ninterface Props {\n  children: ReactNode\n}\n\nexport default function AuthSessionProvider({ children }: Props) {\n  return <SessionProvider>{children}</SessionProvider>\n}\n"], "names": [], "mappings": ";;;;;AAEA;AAFA;;;AASe,SAAS,oBAAoB,KAAmB;QAAnB,EAAE,QAAQ,EAAS,GAAnB;IAC1C,qBAAO,6LAAC,oKAAe;kBAAE;;;;;;AAC3B;KAFwB", "debugId": null}}]}