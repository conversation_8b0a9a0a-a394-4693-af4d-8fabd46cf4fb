var R=require("../../../../chunks/[turbopack]_runtime.js")("server/app/api/auth/[...nextauth]/route.js")
R.c("server/chunks/node_modules_next_026b9b19._.js")
R.c("server/chunks/node_modules_next-auth_1810886e._.js")
R.c("server/chunks/node_modules_openid-client_d15f72b3._.js")
R.c("server/chunks/node_modules_jose_dist_node_cjs_bd88468e._.js")
R.c("server/chunks/node_modules_zod_v4_092dd771._.js")
R.c("server/chunks/node_modules_df404925._.js")
R.c("server/chunks/[root-of-the-server]__12e0b9a8._.js")
R.m("[project]/.next-internal/server/app/api/auth/[...nextauth]/route/actions.js [app-rsc] (server actions loader, ecmascript)")
R.m("[project]/node_modules/next/dist/esm/build/templates/app-route.js { INNER_APP_ROUTE => \"[project]/src/app/api/auth/[...nextauth]/route.ts [app-route] (ecmascript)\" } [app-route] (ecmascript)")
module.exports=R.m("[project]/node_modules/next/dist/esm/build/templates/app-route.js { INNER_APP_ROUTE => \"[project]/src/app/api/auth/[...nextauth]/route.ts [app-route] (ecmascript)\" } [app-route] (ecmascript)").exports
