{"version": 3, "sources": [], "sections": [{"offset": {"line": 4, "column": 0}, "map": {"version": 3, "sources": ["file:///run/media/ceazer/982f53f4-3e3b-4aa6-8864-4f0194b752d01/Rudn/rudn-sudanese-community/node_modules/next/src/client/components/builtin/forbidden.tsx"], "sourcesContent": ["import { HTTPAccessErrorFallback } from '../http-access-fallback/error-fallback'\n\nexport default function Forbidden() {\n  return (\n    <HTTPAccessErrorFallback\n      status={403}\n      message=\"This page could not be accessed.\"\n    />\n  )\n}\n"], "names": ["Forbidden", "HTTPAccessErrorFallback", "status", "message"], "mappings": ";;;+BAEA,WAAA;;;eAAwBA;;;;+BAFgB;AAEzB,SAASA;IACtB,OAAA,WAAA,GACE,CAAA,GAAA,YAAA,GAAA,EAACC,eAAAA,uBAAuB,EAAA;QACtBC,QAAQ;QACRC,SAAQ;;AAGd", "ignoreList": [0], "debugId": null}}]}