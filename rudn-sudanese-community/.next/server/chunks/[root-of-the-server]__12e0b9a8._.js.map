{"version": 3, "sources": [], "sections": [{"offset": {"line": 3, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 115, "column": 0}, "map": {"version": 3, "sources": ["file:///run/media/ceazer/982f53f4-3e3b-4aa6-8864-4f0194b752d01/Rudn/rudn-sudanese-community/src/lib/prisma.ts"], "sourcesContent": ["import { PrismaClient } from '@prisma/client'\n\nconst globalForPrisma = globalThis as unknown as {\n  prisma: PrismaClient | undefined\n}\n\nexport const prisma = globalForPrisma.prisma ?? new PrismaClient()\n\nif (process.env.NODE_ENV !== 'production') globalForPrisma.prisma = prisma\n"], "names": [], "mappings": ";;;;AAAA;;AAEA,MAAM,kBAAkB;AAIjB,MAAM,SAAS,gBAAgB,MAAM,IAAI,IAAI,6IAAY;AAEhE,wCAA2C,gBAAgB,MAAM,GAAG", "debugId": null}}, {"offset": {"line": 128, "column": 0}, "map": {"version": 3, "sources": ["file:///run/media/ceazer/982f53f4-3e3b-4aa6-8864-4f0194b752d01/Rudn/rudn-sudanese-community/src/lib/validations.ts"], "sourcesContent": ["import { z } from 'zod'\n\n// User validation schemas\nexport const registerSchema = z.object({\n  email: z\n    .string()\n    .email('Invalid email address')\n    .refine(\n      (email) => email.endsWith('@rudn.ru') || email.endsWith('@rudn.university'),\n      'Email must be from RUDN domain (@rudn.ru or @rudn.university)'\n    ),\n  password: z\n    .string()\n    .min(8, 'Password must be at least 8 characters')\n    .regex(/[A-Z]/, 'Password must contain at least one uppercase letter')\n    .regex(/[a-z]/, 'Password must contain at least one lowercase letter')\n    .regex(/\\d/, 'Password must contain at least one number'),\n  confirmPassword: z.string(),\n  fullNameEn: z\n    .string()\n    .min(2, 'Full name must be at least 2 characters')\n    .max(100, 'Full name must be less than 100 characters'),\n  fullNameAr: z\n    .string()\n    .max(100, 'Arabic name must be less than 100 characters')\n    .optional(),\n  studentId: z\n    .string()\n    .min(5, 'Student ID must be at least 5 characters')\n    .max(20, 'Student ID must be less than 20 characters'),\n  faculty: z.enum([\n    'Engineering',\n    'Medicine',\n    'Economics',\n    'Law',\n    'Philology',\n    'Physical-Mathematical and Natural Sciences',\n    'Humanities and Social Sciences',\n    'Ecology',\n    'Other'\n  ]),\n  academicYear: z.enum([\n    '1st Year',\n    '2nd Year',\n    '3rd Year',\n    '4th Year',\n    '5th Year',\n    '6th Year',\n    'Master\\'s',\n    'PhD'\n  ]),\n  phone: z\n    .string()\n    .regex(/^\\+?[\\d\\s-()]+$/, 'Invalid phone number format')\n    .optional()\n    .or(z.literal('')),\n}).refine((data) => data.password === data.confirmPassword, {\n  message: \"Passwords don't match\",\n  path: [\"confirmPassword\"],\n})\n\nexport const loginSchema = z.object({\n  email: z.string().email('Invalid email address'),\n  password: z.string().min(1, 'Password is required'),\n})\n\nexport const profileUpdateSchema = z.object({\n  fullNameEn: z\n    .string()\n    .min(2, 'Full name must be at least 2 characters')\n    .max(100, 'Full name must be less than 100 characters')\n    .optional(),\n  fullNameAr: z\n    .string()\n    .max(100, 'Arabic name must be less than 100 characters')\n    .optional(),\n  phone: z\n    .string()\n    .regex(/^\\+?[\\d\\s-()]+$/, 'Invalid phone number format')\n    .optional()\n    .or(z.literal('')),\n  bio: z\n    .string()\n    .max(500, 'Bio must be less than 500 characters')\n    .optional(),\n  interests: z\n    .array(z.string())\n    .max(10, 'Maximum 10 interests allowed')\n    .optional(),\n  socialLinks: z\n    .record(z.string().url('Invalid URL format'))\n    .optional(),\n})\n\n// Event validation schemas\nexport const eventSchema = z.object({\n  title: z\n    .string()\n    .min(3, 'Title must be at least 3 characters')\n    .max(100, 'Title must be less than 100 characters'),\n  description: z\n    .string()\n    .min(10, 'Description must be at least 10 characters')\n    .max(2000, 'Description must be less than 2000 characters'),\n  dateTime: z\n    .string()\n    .refine((date) => new Date(date) > new Date(), 'Event date must be in the future'),\n  location: z\n    .string()\n    .min(3, 'Location must be at least 3 characters')\n    .max(200, 'Location must be less than 200 characters'),\n  category: z.enum([\n    'Academic',\n    'Social',\n    'Cultural',\n    'Sports',\n    'Professional',\n    'Other'\n  ]),\n  capacity: z\n    .number()\n    .min(1, 'Capacity must be at least 1')\n    .max(1000, 'Capacity must be less than 1000')\n    .optional(),\n  registrationDeadline: z\n    .string()\n    .optional()\n    .refine((date) => !date || new Date(date) > new Date(), 'Registration deadline must be in the future'),\n})\n\n// Photo validation schemas\nexport const photoUploadSchema = z.object({\n  albumId: z.string().min(1, 'Album is required'),\n  caption: z\n    .string()\n    .max(500, 'Caption must be less than 500 characters')\n    .optional(),\n  tags: z\n    .array(z.string())\n    .max(10, 'Maximum 10 tags allowed')\n    .optional(),\n})\n\nexport const albumSchema = z.object({\n  name: z\n    .string()\n    .min(2, 'Album name must be at least 2 characters')\n    .max(100, 'Album name must be less than 100 characters'),\n  description: z\n    .string()\n    .max(500, 'Description must be less than 500 characters')\n    .optional(),\n  isPublic: z.boolean().default(true),\n})\n\n// Search and filter schemas\nexport const searchSchema = z.object({\n  query: z.string().max(100, 'Search query too long').optional(),\n  category: z.string().optional(),\n  faculty: z.string().optional(),\n  sortBy: z.enum(['date', 'name', 'created']).default('date'),\n  sortOrder: z.enum(['asc', 'desc']).default('desc'),\n  page: z.number().min(1).default(1),\n  limit: z.number().min(1).max(50).default(10),\n})\n\n// Admin validation schemas\nexport const userModerationSchema = z.object({\n  userId: z.string().min(1, 'User ID is required'),\n  action: z.enum(['approve', 'reject', 'suspend', 'activate']),\n  reason: z.string().max(500, 'Reason must be less than 500 characters').optional(),\n})\n\nexport const contentModerationSchema = z.object({\n  contentId: z.string().min(1, 'Content ID is required'),\n  contentType: z.enum(['event', 'photo', 'comment']),\n  action: z.enum(['approve', 'reject', 'delete']),\n  reason: z.string().max(500, 'Reason must be less than 500 characters').optional(),\n})\n\n// Type exports\nexport type RegisterInput = z.infer<typeof registerSchema>\nexport type LoginInput = z.infer<typeof loginSchema>\nexport type ProfileUpdateInput = z.infer<typeof profileUpdateSchema>\nexport type EventInput = z.infer<typeof eventSchema>\nexport type PhotoUploadInput = z.infer<typeof photoUploadSchema>\nexport type AlbumInput = z.infer<typeof albumSchema>\nexport type SearchInput = z.infer<typeof searchSchema>\nexport type UserModerationInput = z.infer<typeof userModerationSchema>\nexport type ContentModerationInput = z.infer<typeof contentModerationSchema>\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;AAAA;;AAGO,MAAM,iBAAiB,oLAAC,CAAC,MAAM,CAAC;IACrC,OAAO,oLAAC,CACL,MAAM,GACN,KAAK,CAAC,yBACN,MAAM,CACL,CAAC,QAAU,MAAM,QAAQ,CAAC,eAAe,MAAM,QAAQ,CAAC,qBACxD;IAEJ,UAAU,oLAAC,CACR,MAAM,GACN,GAAG,CAAC,GAAG,0CACP,KAAK,CAAC,SAAS,uDACf,KAAK,CAAC,SAAS,uDACf,KAAK,CAAC,MAAM;IACf,iBAAiB,oLAAC,CAAC,MAAM;IACzB,YAAY,oLAAC,CACV,MAAM,GACN,GAAG,CAAC,GAAG,2CACP,GAAG,CAAC,KAAK;IACZ,YAAY,oLAAC,CACV,MAAM,GACN,GAAG,CAAC,KAAK,gDACT,QAAQ;IACX,WAAW,oLAAC,CACT,MAAM,GACN,GAAG,CAAC,GAAG,4CACP,GAAG,CAAC,IAAI;IACX,SAAS,oLAAC,CAAC,IAAI,CAAC;QACd;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;KACD;IACD,cAAc,oLAAC,CAAC,IAAI,CAAC;QACnB;QACA;QACA;QACA;QACA;QACA;QACA;QACA;KACD;IACD,OAAO,oLAAC,CACL,MAAM,GACN,KAAK,CAAC,mBAAmB,+BACzB,QAAQ,GACR,EAAE,CAAC,oLAAC,CAAC,OAAO,CAAC;AAClB,GAAG,MAAM,CAAC,CAAC,OAAS,KAAK,QAAQ,KAAK,KAAK,eAAe,EAAE;IAC1D,SAAS;IACT,MAAM;QAAC;KAAkB;AAC3B;AAEO,MAAM,cAAc,oLAAC,CAAC,MAAM,CAAC;IAClC,OAAO,oLAAC,CAAC,MAAM,GAAG,KAAK,CAAC;IACxB,UAAU,oLAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;AAC9B;AAEO,MAAM,sBAAsB,oLAAC,CAAC,MAAM,CAAC;IAC1C,YAAY,oLAAC,CACV,MAAM,GACN,GAAG,CAAC,GAAG,2CACP,GAAG,CAAC,KAAK,8CACT,QAAQ;IACX,YAAY,oLAAC,CACV,MAAM,GACN,GAAG,CAAC,KAAK,gDACT,QAAQ;IACX,OAAO,oLAAC,CACL,MAAM,GACN,KAAK,CAAC,mBAAmB,+BACzB,QAAQ,GACR,EAAE,CAAC,oLAAC,CAAC,OAAO,CAAC;IAChB,KAAK,oLAAC,CACH,MAAM,GACN,GAAG,CAAC,KAAK,wCACT,QAAQ;IACX,WAAW,oLAAC,CACT,KAAK,CAAC,oLAAC,CAAC,MAAM,IACd,GAAG,CAAC,IAAI,gCACR,QAAQ;IACX,aAAa,oLAAC,CACX,MAAM,CAAC,oLAAC,CAAC,MAAM,GAAG,GAAG,CAAC,uBACtB,QAAQ;AACb;AAGO,MAAM,cAAc,oLAAC,CAAC,MAAM,CAAC;IAClC,OAAO,oLAAC,CACL,MAAM,GACN,GAAG,CAAC,GAAG,uCACP,GAAG,CAAC,KAAK;IACZ,aAAa,oLAAC,CACX,MAAM,GACN,GAAG,CAAC,IAAI,8CACR,GAAG,CAAC,MAAM;IACb,UAAU,oLAAC,CACR,MAAM,GACN,MAAM,CAAC,CAAC,OAAS,IAAI,KAAK,QAAQ,IAAI,QAAQ;IACjD,UAAU,oLAAC,CACR,MAAM,GACN,GAAG,CAAC,GAAG,0CACP,GAAG,CAAC,KAAK;IACZ,UAAU,oLAAC,CAAC,IAAI,CAAC;QACf;QACA;QACA;QACA;QACA;QACA;KACD;IACD,UAAU,oLAAC,CACR,MAAM,GACN,GAAG,CAAC,GAAG,+BACP,GAAG,CAAC,MAAM,mCACV,QAAQ;IACX,sBAAsB,oLAAC,CACpB,MAAM,GACN,QAAQ,GACR,MAAM,CAAC,CAAC,OAAS,CAAC,QAAQ,IAAI,KAAK,QAAQ,IAAI,QAAQ;AAC5D;AAGO,MAAM,oBAAoB,oLAAC,CAAC,MAAM,CAAC;IACxC,SAAS,oLAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;IAC3B,SAAS,oLAAC,CACP,MAAM,GACN,GAAG,CAAC,KAAK,4CACT,QAAQ;IACX,MAAM,oLAAC,CACJ,KAAK,CAAC,oLAAC,CAAC,MAAM,IACd,GAAG,CAAC,IAAI,2BACR,QAAQ;AACb;AAEO,MAAM,cAAc,oLAAC,CAAC,MAAM,CAAC;IAClC,MAAM,oLAAC,CACJ,MAAM,GACN,GAAG,CAAC,GAAG,4CACP,GAAG,CAAC,KAAK;IACZ,aAAa,oLAAC,CACX,MAAM,GACN,GAAG,CAAC,KAAK,gDACT,QAAQ;IACX,UAAU,oLAAC,CAAC,OAAO,GAAG,OAAO,CAAC;AAChC;AAGO,MAAM,eAAe,oLAAC,CAAC,MAAM,CAAC;IACnC,OAAO,oLAAC,CAAC,MAAM,GAAG,GAAG,CAAC,KAAK,yBAAyB,QAAQ;IAC5D,UAAU,oLAAC,CAAC,MAAM,GAAG,QAAQ;IAC7B,SAAS,oLAAC,CAAC,MAAM,GAAG,QAAQ;IAC5B,QAAQ,oLAAC,CAAC,IAAI,CAAC;QAAC;QAAQ;QAAQ;KAAU,EAAE,OAAO,CAAC;IACpD,WAAW,oLAAC,CAAC,IAAI,CAAC;QAAC;QAAO;KAAO,EAAE,OAAO,CAAC;IAC3C,MAAM,oLAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG,OAAO,CAAC;IAChC,OAAO,oLAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG,GAAG,CAAC,IAAI,OAAO,CAAC;AAC3C;AAGO,MAAM,uBAAuB,oLAAC,CAAC,MAAM,CAAC;IAC3C,QAAQ,oLAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;IAC1B,QAAQ,oLAAC,CAAC,IAAI,CAAC;QAAC;QAAW;QAAU;QAAW;KAAW;IAC3D,QAAQ,oLAAC,CAAC,MAAM,GAAG,GAAG,CAAC,KAAK,2CAA2C,QAAQ;AACjF;AAEO,MAAM,0BAA0B,oLAAC,CAAC,MAAM,CAAC;IAC9C,WAAW,oLAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;IAC7B,aAAa,oLAAC,CAAC,IAAI,CAAC;QAAC;QAAS;QAAS;KAAU;IACjD,QAAQ,oLAAC,CAAC,IAAI,CAAC;QAAC;QAAW;QAAU;KAAS;IAC9C,QAAQ,oLAAC,CAAC,MAAM,GAAG,GAAG,CAAC,KAAK,2CAA2C,QAAQ;AACjF", "debugId": null}}, {"offset": {"line": 267, "column": 0}, "map": {"version": 3, "sources": ["file:///run/media/ceazer/982f53f4-3e3b-4aa6-8864-4f0194b752d01/Rudn/rudn-sudanese-community/src/lib/auth.ts"], "sourcesContent": ["import { NextAuthOptions } from 'next-auth'\nimport Cred<PERSON><PERSON><PERSON>rovider from 'next-auth/providers/credentials'\nimport { PrismaAdapter } from '@next-auth/prisma-adapter'\nimport bcrypt from 'bcryptjs'\nimport { prisma } from './prisma'\nimport { loginSchema } from './validations'\n\nexport const authOptions: NextAuthOptions = {\n  adapter: PrismaAdapter(prisma),\n  providers: [\n    CredentialsProvider({\n      name: 'credentials',\n      credentials: {\n        email: { label: 'Email', type: 'email' },\n        password: { label: 'Password', type: 'password' }\n      },\n      async authorize(credentials) {\n        if (!credentials?.email || !credentials?.password) {\n          throw new Error('Email and password are required')\n        }\n\n        // Validate input\n        const validatedFields = loginSchema.safeParse(credentials)\n        if (!validatedFields.success) {\n          throw new Error('Invalid credentials format')\n        }\n\n        const { email, password } = validatedFields.data\n\n        // Find user in database\n        const user = await prisma.user.findUnique({\n          where: { email }\n        })\n\n        if (!user || !user.password) {\n          throw new Error('Invalid credentials')\n        }\n\n        // Check if user is verified\n        if (!user.isVerified) {\n          throw new Error('Account pending verification. Please wait for admin approval.')\n        }\n\n        // Verify password\n        const isPasswordValid = await bcrypt.compare(password, user.password)\n        if (!isPasswordValid) {\n          throw new Error('Invalid credentials')\n        }\n\n        return {\n          id: user.id,\n          email: user.email,\n          name: user.fullNameEn,\n          image: user.profilePicture,\n          isAdmin: user.isAdmin,\n          isVerified: user.isVerified,\n          faculty: user.faculty,\n          studentId: user.studentId\n        }\n      }\n    })\n  ],\n  session: {\n    strategy: 'jwt',\n    maxAge: 30 * 24 * 60 * 60, // 30 days\n  },\n  jwt: {\n    maxAge: 30 * 24 * 60 * 60, // 30 days\n  },\n  callbacks: {\n    async jwt({ token, user }) {\n      if (user) {\n        token.isAdmin = user.isAdmin\n        token.isVerified = user.isVerified\n        token.faculty = user.faculty\n        token.studentId = user.studentId\n      }\n      return token\n    },\n    async session({ session, token }) {\n      if (token) {\n        session.user.id = token.sub!\n        session.user.isAdmin = token.isAdmin as boolean\n        session.user.isVerified = token.isVerified as boolean\n        session.user.faculty = token.faculty as string\n        session.user.studentId = token.studentId as string\n      }\n      return session\n    }\n  },\n  pages: {\n    signIn: '/auth/login',\n    signUp: '/auth/register',\n    error: '/auth/error',\n  },\n  secret: process.env.NEXTAUTH_SECRET,\n}\n\n// Helper function to hash passwords\nexport async function hashPassword(password: string): Promise<string> {\n  return bcrypt.hash(password, 12)\n}\n\n// Helper function to verify passwords\nexport async function verifyPassword(password: string, hashedPassword: string): Promise<boolean> {\n  return bcrypt.compare(password, hashedPassword)\n}\n"], "names": [], "mappings": ";;;;;;;;AACA;AACA;AACA;AACA;AACA;;;;;;AAEO,MAAM,cAA+B;IAC1C,SAAS,IAAA,uLAAa,EAAC,gIAAM;IAC7B,WAAW;QACT,IAAA,qKAAmB,EAAC;YAClB,MAAM;YACN,aAAa;gBACX,OAAO;oBAAE,OAAO;oBAAS,MAAM;gBAAQ;gBACvC,UAAU;oBAAE,OAAO;oBAAY,MAAM;gBAAW;YAClD;YACA,MAAM,WAAU,WAAW;gBACzB,IAAI,CAAC,aAAa,SAAS,CAAC,aAAa,UAAU;oBACjD,MAAM,IAAI,MAAM;gBAClB;gBAEA,iBAAiB;gBACjB,MAAM,kBAAkB,0IAAW,CAAC,SAAS,CAAC;gBAC9C,IAAI,CAAC,gBAAgB,OAAO,EAAE;oBAC5B,MAAM,IAAI,MAAM;gBAClB;gBAEA,MAAM,EAAE,KAAK,EAAE,QAAQ,EAAE,GAAG,gBAAgB,IAAI;gBAEhD,wBAAwB;gBACxB,MAAM,OAAO,MAAM,gIAAM,CAAC,IAAI,CAAC,UAAU,CAAC;oBACxC,OAAO;wBAAE;oBAAM;gBACjB;gBAEA,IAAI,CAAC,QAAQ,CAAC,KAAK,QAAQ,EAAE;oBAC3B,MAAM,IAAI,MAAM;gBAClB;gBAEA,4BAA4B;gBAC5B,IAAI,CAAC,KAAK,UAAU,EAAE;oBACpB,MAAM,IAAI,MAAM;gBAClB;gBAEA,kBAAkB;gBAClB,MAAM,kBAAkB,MAAM,8IAAM,CAAC,OAAO,CAAC,UAAU,KAAK,QAAQ;gBACpE,IAAI,CAAC,iBAAiB;oBACpB,MAAM,IAAI,MAAM;gBAClB;gBAEA,OAAO;oBACL,IAAI,KAAK,EAAE;oBACX,OAAO,KAAK,KAAK;oBACjB,MAAM,KAAK,UAAU;oBACrB,OAAO,KAAK,cAAc;oBAC1B,SAAS,KAAK,OAAO;oBACrB,YAAY,KAAK,UAAU;oBAC3B,SAAS,KAAK,OAAO;oBACrB,WAAW,KAAK,SAAS;gBAC3B;YACF;QACF;KACD;IACD,SAAS;QACP,UAAU;QACV,QAAQ,KAAK,KAAK,KAAK;IACzB;IACA,KAAK;QACH,QAAQ,KAAK,KAAK,KAAK;IACzB;IACA,WAAW;QACT,MAAM,KAAI,EAAE,KAAK,EAAE,IAAI,EAAE;YACvB,IAAI,MAAM;gBACR,MAAM,OAAO,GAAG,KAAK,OAAO;gBAC5B,MAAM,UAAU,GAAG,KAAK,UAAU;gBAClC,MAAM,OAAO,GAAG,KAAK,OAAO;gBAC5B,MAAM,SAAS,GAAG,KAAK,SAAS;YAClC;YACA,OAAO;QACT;QACA,MAAM,SAAQ,EAAE,OAAO,EAAE,KAAK,EAAE;YAC9B,IAAI,OAAO;gBACT,QAAQ,IAAI,CAAC,EAAE,GAAG,MAAM,GAAG;gBAC3B,QAAQ,IAAI,CAAC,OAAO,GAAG,MAAM,OAAO;gBACpC,QAAQ,IAAI,CAAC,UAAU,GAAG,MAAM,UAAU;gBAC1C,QAAQ,IAAI,CAAC,OAAO,GAAG,MAAM,OAAO;gBACpC,QAAQ,IAAI,CAAC,SAAS,GAAG,MAAM,SAAS;YAC1C;YACA,OAAO;QACT;IACF;IACA,OAAO;QACL,QAAQ;QACR,QAAQ;QACR,OAAO;IACT;IACA,QAAQ,QAAQ,GAAG,CAAC,eAAe;AACrC;AAGO,eAAe,aAAa,QAAgB;IACjD,OAAO,8IAAM,CAAC,IAAI,CAAC,UAAU;AAC/B;AAGO,eAAe,eAAe,QAAgB,EAAE,cAAsB;IAC3E,OAAO,8IAAM,CAAC,OAAO,CAAC,UAAU;AAClC", "debugId": null}}, {"offset": {"line": 386, "column": 0}, "map": {"version": 3, "sources": ["file:///run/media/ceazer/982f53f4-3e3b-4aa6-8864-4f0194b752d01/Rudn/rudn-sudanese-community/src/app/api/auth/%5B...nextauth%5D/route.ts"], "sourcesContent": ["import NextAuth from 'next-auth'\nimport { authOptions } from '@/lib/auth'\n\nconst handler = NextAuth(authOptions)\n\nexport { handler as GET, handler as POST }\n"], "names": [], "mappings": ";;;;;;AAAA;AACA;;;AAEA,MAAM,UAAU,IAAA,kJAAQ,EAAC,mIAAW", "debugId": null}}]}