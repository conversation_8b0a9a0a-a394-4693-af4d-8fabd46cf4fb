import { User, Event, Photo, Album, RSVP, RSVPStatus } from '@prisma/client'

export type { User, Event, Photo, Album, RSVP, RSVPStatus }

export interface UserWithDetails extends User {
  _count?: {
    events?: number
    photos?: number
    rsvps?: number
  }
}

export interface EventWithDetails extends Event {
  organizer: User
  rsvps: RSVP[]
  _count?: {
    rsvps?: number
  }
}

export interface PhotoWithDetails extends Photo {
  album: Album
  uploader: User
}

export interface AlbumWithPhotos extends Album {
  photos: Photo[]
  _count?: {
    photos?: number
  }
}

export interface RegisterFormData {
  email: string
  password: string
  confirmPassword: string
  fullNameEn: string
  fullNameAr?: string
  studentId: string
  faculty: string
  academicYear: string
  phone?: string
  verificationDoc?: File
}

export interface LoginFormData {
  email: string
  password: string
}

export interface EventFormData {
  title: string
  description: string
  dateTime: string
  location: string
  category: string
  capacity?: number
  registrationDeadline?: string
  bannerImage?: File
}

export interface PhotoUploadData {
  files: File[]
  albumId: string
  caption?: string
  tags?: string[]
}

export interface ProfileUpdateData {
  fullNameEn?: string
  fullNameAr?: string
  phone?: string
  bio?: string
  socialLinks?: Record<string, string>
  interests?: string[]
  profilePicture?: File
}

export type Faculty = 
  | 'Engineering'
  | 'Medicine'
  | 'Economics'
  | 'Law'
  | 'Philology'
  | 'Physical-Mathematical and Natural Sciences'
  | 'Humanities and Social Sciences'
  | 'Ecology'
  | 'Other'

export type EventCategory = 
  | 'Academic'
  | 'Social'
  | 'Cultural'
  | 'Sports'
  | 'Professional'
  | 'Other'

export type AcademicYear = 
  | '1st Year'
  | '2nd Year'
  | '3rd Year'
  | '4th Year'
  | '5th Year'
  | '6th Year'
  | 'Master\'s'
  | 'PhD'

export interface ApiResponse<T = any> {
  success: boolean
  data?: T
  error?: string
  message?: string
}

export interface PaginationParams {
  page?: number
  limit?: number
  search?: string
  category?: string
  faculty?: string
  sortBy?: string
  sortOrder?: 'asc' | 'desc'
}

export interface PaginatedResponse<T> {
  data: T[]
  pagination: {
    page: number
    limit: number
    total: number
    totalPages: number
    hasNext: boolean
    hasPrev: boolean
  }
}
