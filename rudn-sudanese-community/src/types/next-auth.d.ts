import NextAuth from 'next-auth'

declare module 'next-auth' {
  interface Session {
    user: {
      id: string
      email: string
      name: string
      image?: string
      isAdmin: boolean
      isVerified: boolean
      faculty: string
      studentId: string
    }
  }

  interface User {
    id: string
    email: string
    name: string
    image?: string
    isAdmin: boolean
    isVerified: boolean
    faculty: string
    studentId: string
  }
}

declare module 'next-auth/jwt' {
  interface JWT {
    isAdmin: boolean
    isVerified: boolean
    faculty: string
    studentId: string
  }
}
