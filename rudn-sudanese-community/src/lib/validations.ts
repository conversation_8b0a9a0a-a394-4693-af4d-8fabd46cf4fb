import { z } from 'zod'

// User validation schemas
export const registerSchema = z.object({
  email: z
    .string()
    .email('Invalid email address')
    .refine(
      (email) => email.endsWith('@rudn.ru') || email.endsWith('@rudn.university'),
      'Email must be from RUDN domain (@rudn.ru or @rudn.university)'
    ),
  password: z
    .string()
    .min(8, 'Password must be at least 8 characters')
    .regex(/[A-Z]/, 'Password must contain at least one uppercase letter')
    .regex(/[a-z]/, 'Password must contain at least one lowercase letter')
    .regex(/\d/, 'Password must contain at least one number'),
  confirmPassword: z.string(),
  fullNameEn: z
    .string()
    .min(2, 'Full name must be at least 2 characters')
    .max(100, 'Full name must be less than 100 characters'),
  fullNameAr: z
    .string()
    .max(100, 'Arabic name must be less than 100 characters')
    .optional(),
  studentId: z
    .string()
    .min(5, 'Student ID must be at least 5 characters')
    .max(20, 'Student ID must be less than 20 characters'),
  faculty: z.enum([
    'Engineering',
    'Medicine',
    'Economics',
    'Law',
    'Philology',
    'Physical-Mathematical and Natural Sciences',
    'Humanities and Social Sciences',
    'Ecology',
    'Other'
  ]),
  academicYear: z.enum([
    '1st Year',
    '2nd Year',
    '3rd Year',
    '4th Year',
    '5th Year',
    '6th Year',
    'Master\'s',
    'PhD'
  ]),
  phone: z
    .string()
    .regex(/^\+?[\d\s-()]+$/, 'Invalid phone number format')
    .optional()
    .or(z.literal('')),
}).refine((data) => data.password === data.confirmPassword, {
  message: "Passwords don't match",
  path: ["confirmPassword"],
})

export const loginSchema = z.object({
  email: z.string().email('Invalid email address'),
  password: z.string().min(1, 'Password is required'),
})

export const profileUpdateSchema = z.object({
  fullNameEn: z
    .string()
    .min(2, 'Full name must be at least 2 characters')
    .max(100, 'Full name must be less than 100 characters')
    .optional(),
  fullNameAr: z
    .string()
    .max(100, 'Arabic name must be less than 100 characters')
    .optional(),
  phone: z
    .string()
    .regex(/^\+?[\d\s-()]+$/, 'Invalid phone number format')
    .optional()
    .or(z.literal('')),
  bio: z
    .string()
    .max(500, 'Bio must be less than 500 characters')
    .optional(),
  interests: z
    .array(z.string())
    .max(10, 'Maximum 10 interests allowed')
    .optional(),
  socialLinks: z
    .record(z.string().url('Invalid URL format'))
    .optional(),
})

// Event validation schemas
export const eventSchema = z.object({
  title: z
    .string()
    .min(3, 'Title must be at least 3 characters')
    .max(100, 'Title must be less than 100 characters'),
  description: z
    .string()
    .min(10, 'Description must be at least 10 characters')
    .max(2000, 'Description must be less than 2000 characters'),
  dateTime: z
    .string()
    .refine((date) => new Date(date) > new Date(), 'Event date must be in the future'),
  location: z
    .string()
    .min(3, 'Location must be at least 3 characters')
    .max(200, 'Location must be less than 200 characters'),
  category: z.enum([
    'Academic',
    'Social',
    'Cultural',
    'Sports',
    'Professional',
    'Other'
  ]),
  capacity: z
    .number()
    .min(1, 'Capacity must be at least 1')
    .max(1000, 'Capacity must be less than 1000')
    .optional(),
  registrationDeadline: z
    .string()
    .optional()
    .refine((date) => !date || new Date(date) > new Date(), 'Registration deadline must be in the future'),
})

// Photo validation schemas
export const photoUploadSchema = z.object({
  albumId: z.string().min(1, 'Album is required'),
  caption: z
    .string()
    .max(500, 'Caption must be less than 500 characters')
    .optional(),
  tags: z
    .array(z.string())
    .max(10, 'Maximum 10 tags allowed')
    .optional(),
})

export const albumSchema = z.object({
  name: z
    .string()
    .min(2, 'Album name must be at least 2 characters')
    .max(100, 'Album name must be less than 100 characters'),
  description: z
    .string()
    .max(500, 'Description must be less than 500 characters')
    .optional(),
  isPublic: z.boolean().default(true),
})

// Search and filter schemas
export const searchSchema = z.object({
  query: z.string().max(100, 'Search query too long').optional(),
  category: z.string().optional(),
  faculty: z.string().optional(),
  sortBy: z.enum(['date', 'name', 'created']).default('date'),
  sortOrder: z.enum(['asc', 'desc']).default('desc'),
  page: z.number().min(1).default(1),
  limit: z.number().min(1).max(50).default(10),
})

// Admin validation schemas
export const userModerationSchema = z.object({
  userId: z.string().min(1, 'User ID is required'),
  action: z.enum(['approve', 'reject', 'suspend', 'activate']),
  reason: z.string().max(500, 'Reason must be less than 500 characters').optional(),
})

export const contentModerationSchema = z.object({
  contentId: z.string().min(1, 'Content ID is required'),
  contentType: z.enum(['event', 'photo', 'comment']),
  action: z.enum(['approve', 'reject', 'delete']),
  reason: z.string().max(500, 'Reason must be less than 500 characters').optional(),
})

// Type exports
export type RegisterInput = z.infer<typeof registerSchema>
export type LoginInput = z.infer<typeof loginSchema>
export type ProfileUpdateInput = z.infer<typeof profileUpdateSchema>
export type EventInput = z.infer<typeof eventSchema>
export type PhotoUploadInput = z.infer<typeof photoUploadSchema>
export type AlbumInput = z.infer<typeof albumSchema>
export type SearchInput = z.infer<typeof searchSchema>
export type UserModerationInput = z.infer<typeof userModerationSchema>
export type ContentModerationInput = z.infer<typeof contentModerationSchema>
