'use client'

import { useSession } from 'next-auth/react'
import { useRouter } from 'next/navigation'
import { useEffect } from 'react'

export function useAuth(requireAuth = true) {
  const { data: session, status } = useSession()
  const router = useRouter()

  useEffect(() => {
    if (requireAuth && status === 'unauthenticated') {
      router.push('/auth/login')
    }
  }, [status, requireAuth, router])

  return {
    user: session?.user,
    isLoading: status === 'loading',
    isAuthenticated: status === 'authenticated',
    isAdmin: session?.user?.isAdmin || false,
    isVerified: session?.user?.isVerified || false
  }
}

export function useRequireAuth() {
  return useAuth(true)
}

export function useOptionalAuth() {
  return useAuth(false)
}

export function useRequireAdmin() {
  const auth = useAuth(true)
  const router = useRouter()

  useEffect(() => {
    if (auth.isAuthenticated && !auth.isAdmin) {
      router.push('/dashboard')
    }
  }, [auth.isAuthenticated, auth.isAdmin, router])

  return auth
}

export function useRequireVerified() {
  const auth = useAuth(true)
  const router = useRouter()

  useEffect(() => {
    if (auth.isAuthenticated && !auth.isVerified) {
      router.push('/auth/pending-verification')
    }
  }, [auth.isAuthenticated, auth.isVerified, router])

  return auth
}
