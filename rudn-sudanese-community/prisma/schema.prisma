// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model Account {
  id                String  @id @default(cuid())
  userId            String
  type              String
  provider          String
  providerAccountId String
  refresh_token     String? @db.Text
  access_token      String? @db.Text
  expires_at        Int?
  token_type        String?
  scope             String?
  id_token          String? @db.Text
  session_state     String?

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([provider, providerAccountId])
}

model Session {
  id           String   @id @default(cuid())
  sessionToken String   @unique
  userId       String
  expires      DateTime
  user         User     @relation(fields: [userId], references: [id], onDelete: Cascade)
}

model User {
  id                String    @id @default(cuid())
  email             String    @unique
  emailVerified     DateTime?
  password          String?
  fullNameEn        String
  fullNameAr        String?
  studentId         String    @unique
  faculty           String
  academicYear      String
  phone             String?
  profilePicture    String?
  bio               String?
  socialLinks       Json?
  interests         String[]
  isVerified        Boolean   @default(false)
  isAdmin           <PERSON>   @default(false)
  nationality       String    @default("Sudanese")
  verificationDoc   String?
  createdAt         DateTime  @default(now())
  updatedAt         DateTime  @updatedAt

  accounts Account[]
  sessions Session[]
  events   Event[]
  rsvps    RSVP[]
  photos   Photo[]

  @@map("users")
}

model VerificationToken {
  identifier String
  token      String   @unique
  expires    DateTime

  @@unique([identifier, token])
}

model Event {
  id                 String    @id @default(cuid())
  title              String
  description        String    @db.Text
  dateTime           DateTime
  location           String
  category           String
  capacity           Int?
  registrationDeadline DateTime?
  bannerImage        String?
  isApproved         Boolean   @default(false)
  organizerId        String
  createdAt          DateTime  @default(now())
  updatedAt          DateTime  @updatedAt

  organizer User   @relation(fields: [organizerId], references: [id])
  rsvps     RSVP[]

  @@map("events")
}

model RSVP {
  id        String     @id @default(cuid())
  userId    String
  eventId   String
  status    RSVPStatus @default(PENDING)
  createdAt DateTime   @default(now())

  user  User  @relation(fields: [userId], references: [id])
  event Event @relation(fields: [eventId], references: [id])

  @@unique([userId, eventId])
  @@map("rsvps")
}

model Album {
  id          String  @id @default(cuid())
  name        String
  description String?
  isPublic    Boolean @default(true)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  photos Photo[]

  @@map("albums")
}

model Photo {
  id          String   @id @default(cuid())
  filename    String
  caption     String?
  albumId     String
  uploaderId  String
  isApproved  Boolean  @default(false)
  tags        String[]
  createdAt   DateTime @default(now())

  album    Album @relation(fields: [albumId], references: [id])
  uploader User  @relation(fields: [uploaderId], references: [id])

  @@map("photos")
}

enum RSVPStatus {
  PENDING
  CONFIRMED
  CANCELLED
}
